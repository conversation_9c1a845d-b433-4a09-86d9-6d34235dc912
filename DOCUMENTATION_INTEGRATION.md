# Documentation Integration

This document describes the integration between the Java Play Framework application and the Node.js documentation web server (`doc_web`).

## Overview

The Java Play Framework application can now automatically start and manage the Node.js documentation server (`doc_web`) and redirect users to it when they access the root URL (`/`).

## How It Works

1. **Automatic Startup**: When the Play application starts, it automatically starts the Node.js documentation server if enabled in configuration.

2. **Root Route Redirection**: When users access the root URL (`/`), the `Application.index()` method:
   - Checks if the documentation service is enabled
   - Ensures the Node.js server is running
   - Redirects the user to the documentation server

3. **Graceful Fallback**: If the documentation service fails to start or is disabled, the application falls back to serving the default index page.

4. **Automatic Shutdown**: When the Play application shuts down, it automatically stops the Node.js documentation server.

## Configuration

Add the following configuration to your `application.conf` files:

```hocon
# Documentation Web Server Configuration
doc_web = {
    enabled = true                          # Enable/disable the documentation service
    port = 3001                            # Port for the Node.js server
    node_executable = "node"               # Path to Node.js executable
    working_directory = "doc_web"          # Directory containing the Node.js app
    startup_timeout_seconds = 30           # Timeout for server startup
    health_check_timeout_seconds = 5       # Timeout for health checks
    auto_restart = true                    # Auto-restart on failure (future feature)
}
```

## Files Modified/Created

### New Files
- `app/services/documentation/DocumentationService.java` - Service to manage the Node.js server
- `test/services/documentation/DocumentationServiceTest.java` - Unit tests
- `test/controllers/ApplicationControllerTest.java` - Controller tests

### Modified Files
- `app/controllers/Application.java` - Updated to use DocumentationService
- `app/global/ApplicationStart.java` - Added documentation service initialization
- `conf/application.conf` - Added documentation configuration
- `conf/production.application.conf` - Added documentation configuration
- `config/application.conf.prod` - Added documentation configuration

## Usage

### Development Environment

1. Ensure Node.js is installed and available in your PATH
2. Ensure the `doc_web` directory exists and contains a valid Node.js application
3. Start the Play application normally: `./activator run`
4. Access `http://localhost:9000/` - you should be redirected to the documentation

### Production Environment

1. Ensure Node.js is installed on the production server
2. Ensure the `doc_web` directory is deployed alongside the Play application
3. The documentation service will start automatically with the Play application

### Disabling the Documentation Service

To disable the documentation service, set `doc_web.enabled = false` in your configuration. The root route will then serve the default index page.

## Architecture

```
┌─────────────────┐    HTTP Request     ┌──────────────────┐
│   User Browser  │ ──────────────────> │  Play Framework  │
└─────────────────┘                     │   (Port 9000)    │
         ^                              └──────────────────┘
         │                                       │
         │ HTTP Redirect                         │ Process Management
         │ (to port 3001)                        │ & Health Checks
         │                                       v
         │                              ┌──────────────────┐
         └────────────────────────────── │   Node.js App    │
                                        │   (Port 3001)    │
                                        │     doc_web      │
                                        └──────────────────┘
```

## Error Handling

- **Node.js not found**: Service logs error and falls back to default page
- **doc_web directory missing**: Service logs error and falls back to default page
- **Port already in use**: Service logs error and falls back to default page
- **Node.js process crashes**: Service detects and logs the failure, falls back to default page

## Logging

The DocumentationService provides detailed logging:
- Service startup/shutdown events
- Health check results
- Error conditions
- Node.js process output (prefixed with `[doc_web]`)

## Testing

Run the tests with:
```bash
./activator test
```

Specific test classes:
- `test.services.documentation.DocumentationServiceTest`
- `test.controllers.ApplicationControllerTest`

## Troubleshooting

### Documentation service not starting
1. Check that Node.js is installed: `node --version`
2. Check that the `doc_web` directory exists
3. Check the Play application logs for error messages
4. Verify the configuration in `application.conf`

### Port conflicts
1. Change the `doc_web.port` configuration to an available port
2. Ensure no other services are using the configured port

### Permission issues
1. Ensure the Play application has permission to execute Node.js
2. Ensure the Play application has read/write access to the `doc_web` directory

## Future Enhancements

- Auto-restart functionality for crashed Node.js processes
- Load balancing for multiple documentation server instances
- SSL/TLS support for the documentation server
- Integration with Play's asset pipeline for documentation resources
