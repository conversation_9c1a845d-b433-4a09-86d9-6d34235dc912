package global;

import java.util.concurrent.CompletableFuture;
import java.util.logging.LogManager;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.slf4j.bridge.SLF4JBridgeHandler;
import play.Logger;
import play.inject.ApplicationLifecycle;
import services.documentation.DocumentationService;

@Singleton
public class ApplicationStart {

    private static final Logger.ALogger logger = Logger.of(ApplicationStart.class);

    @Inject
    public ApplicationStart(ApplicationLifecycle lifecycle, DocumentationService documentationService) {
        Logger.info("Routing JUL to SLF4J...");
        LogManager.getLogManager().reset();
        SLF4JBridgeHandler.removeHandlersForRootLogger();
        SLF4JBridgeHandler.install();
        Logger.info("Routing completed.");

        // Initialize documentation service
        initializeDocumentationService(documentationService);

        // Register shutdown hook for documentation service
        lifecycle.addStopHook(() -> {
            logger.info("Application shutting down, stopping documentation service...");
            documentationService.stopServer();
            return CompletableFuture.completedFuture(null);
        });
    }

    private void initializeDocumentationService(DocumentationService documentationService) {
        try {
            if (documentationService.isEnabled()) {
                logger.info("Starting documentation service during application startup...");
                documentationService.startServer().whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.error("Failed to start documentation service during startup", throwable);
                    } else {
                        logger.info("Documentation service started successfully during startup");
                    }
                });
            } else {
                logger.info("Documentation service is disabled, skipping startup");
            }
        } catch (Exception e) {
            logger.error("Error initializing documentation service", e);
        }
    }
}
