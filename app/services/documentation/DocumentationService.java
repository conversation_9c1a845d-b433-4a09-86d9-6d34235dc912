package services.documentation;

import global.APIException;
import global.APIException.APIErrors;
import play.Configuration;
import play.Logger;
import play.Play;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Service to manage the Node.js documentation web server (doc_web).
 * This service handles starting, stopping, and health checking of the documentation server.
 */
@Singleton
public class DocumentationService {

    private static final Logger.ALogger logger = Logger.of(DocumentationService.class);

    private final Configuration config;
    private Process docWebProcess;
    private boolean isEnabled;
    private int port;
    private String nodeExecutable;
    private String workingDirectory;
    private int startupTimeoutSeconds;
    private int healthCheckTimeoutSeconds;
    private boolean autoRestart;
    private volatile boolean isStarting = false;

    @Inject
    public DocumentationService() {
        this.config = Play.application().configuration();
        loadConfiguration();
    }

    private void loadConfiguration() {
        this.isEnabled = config.getBoolean("doc_web.enabled");
        this.port = config.getInt("doc_web.port");
        this.nodeExecutable = config.getString("doc_web.node_executable");
        this.workingDirectory = config.getString("doc_web.working_directory");
        this.startupTimeoutSeconds = config.getInt("doc_web.startup_timeout_seconds");
        this.healthCheckTimeoutSeconds = config.getInt("doc_web.health_check_timeout_seconds");
        this.autoRestart = config.getBoolean("doc_web.auto_restart");

        logger.info("DocumentationService configured - enabled: {}, port: {}, working_directory: {}",
            isEnabled, port, workingDirectory);
    }

    /**
     * Starts the documentation web server if it's not already running.
     * @return CompletableFuture that completes when the server is ready
     */
    public CompletableFuture<Void> startServer() {
        if (!isEnabled) {
            logger.warn("Documentation service is disabled in configuration");
            return CompletableFuture.completedFuture(null);
        }

        if (isRunning()) {
            logger.debug("Documentation server is already running");
            return CompletableFuture.completedFuture(null);
        }

        if (isStarting) {
            logger.debug("Documentation server is already starting");
            return waitForServerReady();
        }

        return CompletableFuture.supplyAsync(() -> {
            synchronized (this) {
                if (isStarting || isRunning()) {
                    return null;
                }
                isStarting = true;
            }

            try {
                logger.info("Starting documentation web server on port {}", port);

                File workDir = new File(workingDirectory);
                if (!workDir.exists()) {
                    throw new RuntimeException("Working directory does not exist: " + workingDirectory);
                }

                ProcessBuilder processBuilder = new ProcessBuilder(nodeExecutable, "index.js");
                processBuilder.directory(workDir);
                processBuilder.environment().put("PORT", String.valueOf(port));

                // Redirect error stream to output stream for easier logging
                processBuilder.redirectErrorStream(true);

                docWebProcess = processBuilder.start();

                // Start a thread to log the output
                startOutputLogger();

                // Wait for the server to be ready
                waitForServerReadyBlocking();

                logger.info("Documentation web server started successfully on port {}", port);
                return null;

            } catch (Exception e) {
                logger.error("Failed to start documentation web server", e);
                throw new RuntimeException("Failed to start documentation web server", e);
            } finally {
                isStarting = false;
            }
        });
    }

    /**
     * Stops the documentation web server.
     */
    public void stopServer() {
        if (docWebProcess != null && docWebProcess.isAlive()) {
            logger.info("Stopping documentation web server");
            docWebProcess.destroy();

            try {
                // Wait up to 10 seconds for graceful shutdown
                if (!docWebProcess.waitFor(10, TimeUnit.SECONDS)) {
                    logger.warn("Documentation server did not stop gracefully, forcing termination");
                    docWebProcess.destroyForcibly();
                }
                logger.info("Documentation web server stopped");
            } catch (InterruptedException e) {
                logger.error("Interrupted while waiting for documentation server to stop", e);
                Thread.currentThread().interrupt();
            }

            docWebProcess = null;
        }
    }

    /**
     * Checks if the documentation server is running and responding.
     * @return true if the server is running and healthy
     */
    public boolean isRunning() {
        if (docWebProcess == null || !docWebProcess.isAlive()) {
            return false;
        }

        return isHealthy();
    }

    /**
     * Performs a health check on the documentation server.
     * @return true if the server responds to HTTP requests
     */
    public boolean isHealthy() {
        try {
            URL url = new URL("http://localhost:" + port + "/");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(healthCheckTimeoutSeconds * 1000);
            connection.setReadTimeout(healthCheckTimeoutSeconds * 1000);

            int responseCode = connection.getResponseCode();
            return responseCode >= 200 && responseCode < 300;

        } catch (Exception e) {
            logger.debug("Health check failed for documentation server: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Gets the URL where the documentation server is running.
     * @return the base URL of the documentation server
     */
    public String getServerUrl() {
        return "http://localhost:" + port;
    }

    /**
     * Gets the port where the documentation server is running.
     * @return the port number
     */
    public int getPort() {
        return port;
    }

    /**
     * Checks if the documentation service is enabled.
     * @return true if enabled in configuration
     */
    public boolean isEnabled() {
        return isEnabled;
    }

    private CompletableFuture<Void> waitForServerReady() {
        return CompletableFuture.runAsync(() -> waitForServerReadyBlocking());
    }

    private void waitForServerReadyBlocking() {
        long startTime = System.currentTimeMillis();
        long timeoutMillis = startupTimeoutSeconds * 1000L;

        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            if (isHealthy()) {
                return;
            }

            try {
                Thread.sleep(500); // Check every 500ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Interrupted while waiting for documentation server", e);
            }
        }

        throw new RuntimeException("Documentation server did not start within " + startupTimeoutSeconds + " seconds");
    }

    private void startOutputLogger() {
        if (docWebProcess == null) return;

        Thread loggerThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(docWebProcess.getInputStream()))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("[doc_web] {}", line);
                }
            } catch (IOException e) {
                logger.debug("Documentation server output stream closed: {}", e.getMessage());
            }
        });

        loggerThread.setDaemon(true);
        loggerThread.setName("doc-web-logger");
        loggerThread.start();
    }

    /**
     * Ensures the documentation server is running, starting it if necessary.
     * This method is synchronous and will block until the server is ready.
     *
     * @throws APIException if the server cannot be started
     */
    public void ensureServerRunning() throws APIException {
        if (!isEnabled) {
            return;
        }

        if (isRunning()) {
            return;
        }

        try {
            startServer().get(startupTimeoutSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("Failed to ensure documentation server is running", e);
            throw APIException.raise(APIErrors.INTERNAL_SERVER_ERROR, e);
        }
    }
}
