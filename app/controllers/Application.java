package controllers;

import global.APIException;
import play.Logger;
import play.mvc.*;
import services.documentation.DocumentationService;
import views.html.*;

import javax.inject.Inject;

public class Application extends Controller {

    private static final Logger.ALogger logger = Logger.of(Application.class);

    @Inject
    private DocumentationService documentationService;

    public Result index() {
        try {
            // Check if documentation service is enabled
            if (!documentationService.isEnabled()) {
                logger.info("Documentation service is disabled, serving default index page");
                return ok(index.render("LAVOMAT"));
            }

            // Ensure the documentation server is running
            documentationService.ensureServerRunning();

            // Redirect to the documentation server
            String docUrl = documentationService.getServerUrl();
            logger.info("Redirecting to documentation server at: {}", docUrl);
            return redirect(docUrl);

        } catch (APIException e) {
            logger.error("Failed to start documentation server, serving default page", e);
            return ok(index.render("LAVOMAT - Documentation Unavailable"));
        } catch (Exception e) {
            logger.error("Unexpected error while handling index request", e);
            return ok(index.render("LAVOMAT - Error"));
        }
    }

    public Result login() {
        return ok(login.render("LAVOMAT | Login"));
    }
}
