package services.documentation;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import play.Application;
import play.Play;
import test.common.BaseTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DocumentationServiceTest extends BaseTest {

    @Mock
    private Application mockApplication;

    @Mock
    private Config mockConfig;

    private DocumentationService documentationService;
    private MockedStatic<Play> playMockedStatic;

    @Before
    public void setUp() {
        super.setUp();
        
        // Mock Play.application()
        playMockedStatic = Mockito.mockStatic(Play.class);
        playMockedStatic.when(Play::application).thenReturn(mockApplication);
        when(mockApplication.configuration()).thenReturn(mockConfig);
        
        // Setup default configuration values
        when(mockConfig.getBoolean("doc_web.enabled")).thenReturn(true);
        when(mockConfig.getInt("doc_web.port")).thenReturn(3001);
        when(mockConfig.getString("doc_web.node_executable")).thenReturn("node");
        when(mockConfig.getString("doc_web.working_directory")).thenReturn("doc_web");
        when(mockConfig.getInt("doc_web.startup_timeout_seconds")).thenReturn(30);
        when(mockConfig.getInt("doc_web.health_check_timeout_seconds")).thenReturn(5);
        when(mockConfig.getBoolean("doc_web.auto_restart")).thenReturn(true);
    }

    @After
    public void tearDown() {
        if (documentationService != null) {
            documentationService.stopServer();
        }
        if (playMockedStatic != null) {
            playMockedStatic.close();
        }
        super.tearDown();
    }

    @Test
    public void testServiceInitialization() {
        documentationService = new DocumentationService();
        
        assertTrue("Service should be enabled", documentationService.isEnabled());
        assertEquals("Port should be 3001", 3001, documentationService.getPort());
        assertEquals("Server URL should be correct", "http://localhost:3001", documentationService.getServerUrl());
    }

    @Test
    public void testServiceDisabled() {
        when(mockConfig.getBoolean("doc_web.enabled")).thenReturn(false);
        
        documentationService = new DocumentationService();
        
        assertFalse("Service should be disabled", documentationService.isEnabled());
        assertFalse("Service should not be running when disabled", documentationService.isRunning());
    }

    @Test
    public void testConfigurationLoading() {
        // Test with custom configuration
        when(mockConfig.getBoolean("doc_web.enabled")).thenReturn(true);
        when(mockConfig.getInt("doc_web.port")).thenReturn(4000);
        when(mockConfig.getString("doc_web.node_executable")).thenReturn("/usr/bin/node");
        when(mockConfig.getString("doc_web.working_directory")).thenReturn("custom_doc_web");
        when(mockConfig.getInt("doc_web.startup_timeout_seconds")).thenReturn(60);
        when(mockConfig.getInt("doc_web.health_check_timeout_seconds")).thenReturn(10);
        when(mockConfig.getBoolean("doc_web.auto_restart")).thenReturn(false);
        
        documentationService = new DocumentationService();
        
        assertTrue("Service should be enabled", documentationService.isEnabled());
        assertEquals("Port should be 4000", 4000, documentationService.getPort());
        assertEquals("Server URL should be correct", "http://localhost:4000", documentationService.getServerUrl());
    }

    @Test
    public void testHealthCheckWhenNotRunning() {
        documentationService = new DocumentationService();
        
        assertFalse("Health check should fail when server is not running", documentationService.isHealthy());
        assertFalse("Service should not be running", documentationService.isRunning());
    }

    @Test
    public void testStopServerWhenNotRunning() {
        documentationService = new DocumentationService();
        
        // This should not throw an exception
        documentationService.stopServer();
        
        assertFalse("Service should not be running", documentationService.isRunning());
    }

    @Test
    public void testEnsureServerRunningWhenDisabled() {
        when(mockConfig.getBoolean("doc_web.enabled")).thenReturn(false);
        
        documentationService = new DocumentationService();
        
        try {
            documentationService.ensureServerRunning();
            fail("Should throw APIException when service is disabled");
        } catch (Exception e) {
            assertTrue("Should throw APIException", e.getMessage().contains("Documentation service is disabled"));
        }
    }

    /**
     * Integration test that requires Node.js to be installed and doc_web directory to exist.
     * This test is disabled by default but can be enabled for manual testing.
     */
    // @Test
    public void testActualServerStartup() throws Exception {
        // This test would require actual Node.js installation and doc_web directory
        // Only enable for integration testing
        
        documentationService = new DocumentationService();
        
        if (documentationService.isEnabled()) {
            documentationService.startServer().get();
            
            assertTrue("Server should be running", documentationService.isRunning());
            assertTrue("Server should be healthy", documentationService.isHealthy());
            
            documentationService.stopServer();
            
            assertFalse("Server should be stopped", documentationService.isRunning());
        }
    }
}
