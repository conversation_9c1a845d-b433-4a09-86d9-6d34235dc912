package controllers;

import global.APIException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import play.mvc.Result;
import services.documentation.DocumentationService;
import test.common.BaseTest;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static play.test.Helpers.*;

@RunWith(MockitoJUnitRunner.class)
public class ApplicationControllerTest extends BaseTest {

    @Mock
    private DocumentationService mockDocumentationService;

    @InjectMocks
    private Application applicationController;

    @Before
    public void setUp() {
        super.setUp();
    }

    @Test
    public void testIndexWithDocumentationServiceDisabled() {
        when(mockDocumentationService.isEnabled()).thenReturn(false);

        Result result = applicationController.index();

        assertEquals("Should return OK status", OK, result.status());
        verify(mockDocumentationService, times(1)).isEnabled();
        verify(mockDocumentationService, never()).ensureServerRunning();
    }

    @Test
    public void testIndexWithDocumentationServiceEnabled() throws APIException {
        when(mockDocumentationService.isEnabled()).thenReturn(true);
        when(mockDocumentationService.getServerUrl()).thenReturn("http://localhost:3001");
        doNothing().when(mockDocumentationService).ensureServerRunning();

        Result result = applicationController.index();

        assertEquals("Should return redirect status", SEE_OTHER, result.status());
        verify(mockDocumentationService, times(1)).isEnabled();
        verify(mockDocumentationService, times(1)).ensureServerRunning();
        verify(mockDocumentationService, times(1)).getServerUrl();
    }

    @Test
    public void testIndexWithDocumentationServiceError() throws APIException {
        when(mockDocumentationService.isEnabled()).thenReturn(true);
        doThrow(new APIException("Test error")).when(mockDocumentationService).ensureServerRunning();

        Result result = applicationController.index();

        assertEquals("Should return OK status when service fails", OK, result.status());
        verify(mockDocumentationService, times(1)).isEnabled();
        verify(mockDocumentationService, times(1)).ensureServerRunning();
        verify(mockDocumentationService, never()).getServerUrl();
    }

    @Test
    public void testIndexWithUnexpectedError() throws APIException {
        when(mockDocumentationService.isEnabled()).thenReturn(true);
        doThrow(new RuntimeException("Unexpected error")).when(mockDocumentationService).ensureServerRunning();

        Result result = applicationController.index();

        assertEquals("Should return OK status when unexpected error occurs", OK, result.status());
        verify(mockDocumentationService, times(1)).isEnabled();
        verify(mockDocumentationService, times(1)).ensureServerRunning();
        verify(mockDocumentationService, never()).getServerUrl();
    }

    @Test
    public void testLogin() {
        Result result = applicationController.login();

        assertEquals("Should return OK status", OK, result.status());
        verifyNoInteractions(mockDocumentationService);
    }
}
